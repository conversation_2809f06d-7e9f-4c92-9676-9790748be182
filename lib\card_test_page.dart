import 'package:flutter/material.dart';
import 'package:four_leaf_poker/table/extras/card_painter.dart';

class CardTestPage extends StatelessWidget {
  const CardTestPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Card Layout Test'),
        backgroundColor: Colors.green.shade700,
      ),
      body: Container(
        color: Colors.green.shade800,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'Desktop Layout (rank in corners, suit in center)',
                style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  PlayingCardWidget.community(
                    rank: 'A',
                    suit: '♠',
                    isMobile: false,
                    width: 80,
                    height: 120,
                  ),
                  const SizedBox(width: 10),
                  PlayingCardWidget.community(
                    rank: 'K',
                    suit: '♥',
                    isMobile: false,
                    width: 80,
                    height: 120,
                  ),
                  const SizedBox(width: 10),
                  PlayingCardWidget.community(
                    rank: 'Q',
                    suit: '♦',
                    isMobile: false,
                    width: 80,
                    height: 120,
                  ),
                  const SizedBox(width: 10),
                  PlayingCardWidget.community(
                    rank: 'J',
                    suit: '♣',
                    isMobile: false,
                    width: 80,
                    height: 120,
                  ),
                ],
              ),
              const SizedBox(height: 40),
              const Text(
                'Mobile Layout (rank upper left, suit lower right)',
                style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  PlayingCardWidget.community(
                    rank: 'A',
                    suit: '♠',
                    isMobile: true,
                    width: 80,
                    height: 120,
                  ),
                  const SizedBox(width: 10),
                  PlayingCardWidget.community(
                    rank: 'K',
                    suit: '♥',
                    isMobile: true,
                    width: 80,
                    height: 120,
                  ),
                  const SizedBox(width: 10),
                  PlayingCardWidget.community(
                    rank: 'Q',
                    suit: '♦',
                    isMobile: true,
                    width: 80,
                    height: 120,
                  ),
                  const SizedBox(width: 10),
                  PlayingCardWidget.community(
                    rank: 'J',
                    suit: '♣',
                    isMobile: true,
                    width: 80,
                    height: 120,
                  ),
                ],
              ),
              const SizedBox(height: 40),
              const Text(
                'Small Mobile Cards (30x45)',
                style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  PlayingCardWidget.community(
                    rank: 'A',
                    suit: '♠',
                    isMobile: true,
                    width: 30,
                    height: 45,
                  ),
                  const SizedBox(width: 5),
                  PlayingCardWidget.community(
                    rank: 'K',
                    suit: '♥',
                    isMobile: true,
                    width: 30,
                    height: 45,
                  ),
                  const SizedBox(width: 5),
                  PlayingCardWidget.community(
                    rank: 'Q',
                    suit: '♦',
                    isMobile: true,
                    width: 30,
                    height: 45,
                  ),
                  const SizedBox(width: 5),
                  PlayingCardWidget.community(
                    rank: 'J',
                    suit: '♣',
                    isMobile: true,
                    width: 30,
                    height: 45,
                  ),
                  const SizedBox(width: 5),
                  PlayingCardWidget.community(
                    rank: '10',
                    suit: '♠',
                    isMobile: true,
                    width: 30,
                    height: 45,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
