import 'dart:math' as Math;

import 'package:flutter/material.dart';
import 'package:four_leaf_poker/models.dart';

import 'extras/card_painter.dart';

class PlayerComponent extends StatelessWidget {
  final Player player;
  String seatPosition = '';
  final Widget amountOnTable;
  bool isCurrentTurn;
  static const double _turnRingWidth = 5.5;
  final int mySeat;
  final String lastMove;
  final int numPlayers;
  PlayerComponent(
      {super.key,
      required this.player,
      required String this.seatPosition,
      required Widget this.amountOnTable,
      required this.mySeat,
      required AnimationController this.turnTimerController,
      required bool this.isCurrentTurn,
      required int this.seatIndex,
      required String this.lastMove,
      required int this.numPlayers});
  var suits = {
    'Hearts': '♥',
    'Diamonds': '♦',
    'Clubs': '♣',
    'Spades': '♠',
  };

  AnimationController turnTimerController;

  int seatIndex;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 250,
      height: 250,
      child: Stack(
        clipBehavior: Clip.none,
        alignment: Alignment.bottomCenter,
        children: [
          // Cards image overlayed at the bottom
          if (player.hand.isNotEmpty) ...[
            if (player.hand.length > 0)
              Positioned(
                top: 20,
                right: 125,
                child: Transform.rotate(
                  angle: -Math.pi / 12,
                  child: PlayingCardWidget.hand(
                      rank: player.hand.isNotEmpty ? player.hand[0].rank : '',
                      suit: player.hand.isNotEmpty
                          ? suits[player.hand[0].suit] ?? ''
                          : ''),
                ),
              ),
            if (player.hand.length > 1)
              Positioned(
                top: 20,
                left: 125,
                child: Transform.rotate(
                  angle: Math.pi / 12,
                  child: PlayingCardWidget.hand(
                      rank: player.hand.isNotEmpty ? player.hand[1].rank : '',
                      suit: player.hand.isNotEmpty
                          ? suits[player.hand[1].suit] ?? ''
                          : ''),
                ),
              )
          ],
          Positioned(
            top: 100,
            left: 59,
            child: Image.asset(
              'assets/images/p1.png',
              width: 125,
              //height: 150,
            ),
          ),

          if (isCurrentTurn)
            Positioned(
                top: 99,
                left: 125 - 68,
                //bottom: 25,
                child: Transform.rotate(
                  angle: -Math.pi * 0.75,
                  child: CircularProgressIndicator(
                      value: (1.0 - turnTimerController.value) * .75,
                      strokeWidth: _turnRingWidth,
                      color: Colors.grey.shade400,
                      backgroundColor: Colors.transparent,
                      constraints: BoxConstraints.tight(const Size(130, 130))),
                )),

          if (numPlayers == 2 && seatPosition == 'SB')
            Positioned(
                top: 203,
                left: 200,
                child: Stack(children: [
                  Image.asset(
                    'assets/images/Player-Position.png',
                    width: 30,
                    //height: 150,
                  ),

                  // Text(
                  //   "D",
                  //   style: TextStyle(
                  //     fontSize: 12,
                  //     fontWeight: FontWeight.w900,
                  //     foreground: Paint()
                  //       ..style = PaintingStyle.stroke
                  //       ..strokeWidth = 4
                  //       ..color = Colors.black,
                  //   ),
                  //   textAlign: TextAlign.center,
                  // ),
                  // Text(
                  //   "D",
                  //   style: const TextStyle(
                  //     fontSize: 12,
                  //     fontWeight: FontWeight.w900,
                  //     color: Colors.white,
                  //   ),
                  //   textAlign: TextAlign.center,
                  // )
                ])), // Player image as background
          Positioned.fill(
            top: 100,
            //left: 10,
            child: Image.asset(
              'assets/images/PlayerFrame.png',
              width: 200,
              // fit: BoxFit.contain,
            ),
          ),
          Positioned(
              top: 211,
              child: Stack(children: [
                Text(
                  player.name,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w900,
                    foreground: Paint()
                      ..style = PaintingStyle.stroke
                      ..strokeWidth = 4
                      ..color = Colors.black,
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  player.name,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w900,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                )
              ])),

          Positioned(
              top: 210,
              left: seatPosition == 'D' ? 179 : 170,
              child: Stack(children: [
                Text(
                  seatPosition,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w900,
                    foreground: Paint()
                      ..style = PaintingStyle.stroke
                      ..strokeWidth = 4
                      ..color = Colors.black,
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  seatPosition,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w900,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                )
              ])),
          if (player.contributedThisStreet > 0)
            Positioned(
              top: chip_pos['0']?['top'] as double,
              left: chip_pos['0']?['left'] as double,
              child: amountOnTable,
            ),
          if ((turnTimerController.duration!.inSeconds).round() > 0 &&
              ((1.0 - turnTimerController.value) *
                          turnTimerController.duration!.inSeconds)
                      .round() <
                  10)
            Positioned(
              top: 150,
              left: 125 - 5,
              child: AnimatedBuilder(
                animation: turnTimerController,
                builder: (context, child) {
                  return Opacity(
                    opacity: 1,
                    child: Text(
                      '${((1.0 - turnTimerController.value) * turnTimerController.duration!.inSeconds).round().toString()}',
                      style: TextStyle(color: Colors.red, fontSize: 22),
                    ),
                  );
                },
              ),
            ),

          Positioned(
            top: 125,
            left: 155,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
              decoration: BoxDecoration(
                color: const Color.fromARGB(255, 226, 226, 226),
                borderRadius: BorderRadius.circular(50),
                border: Border.all(
                    color: const Color.fromARGB(255, 0, 0, 0).withOpacity(0.2)),
              ),
              child: Text(
                '\$${player.chips}',
                style: const TextStyle(
                  color: Color.fromARGB(255, 0, 0, 0),
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          Stack(alignment: Alignment.center, children: [
            Text(
              lastMove,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w900,
                foreground: Paint()
                  ..style = PaintingStyle.stroke
                  ..strokeWidth = 4
                  ..color = Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              lastMove,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w900,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            )
          ]),

          // Text(' ${((  (seatIndex > mySeat) ? seatIndex - mySeat : (9-( mySeat - seatIndex)))%9 ).toString()  }    ${(( seatIndex) ).toString()  }     ${  (( mySeat) ).toString()}', style: TextStyle(color: Colors.blue, fontSize: 12),),
        ],
      ),
    );
  }
}

var chip_pos = {
  '0': {
    'top': -35,
    'left': 125 - 16,
  }
};
