import 'package:flutter/material.dart';
import 'HeaderComponent.dart';
import 'GameListComponent.dart';
import 'FooterComponent.dart';
import '../services/game_service.dart';

class GameLobbyLayout extends StatefulWidget {
  const GameLobbyLayout({super.key});

  @override
  State<GameLobbyLayout> createState() => _GameLobbyLayoutState();
}

class _GameLobbyLayoutState extends State<GameLobbyLayout> {
  List<Map<String, String>> games = [];
  bool isLoading = true;
  List<Map<String, String>> gamesFromHive = [];
  @override 
  void initState() {
    super.initState();
    _loadGames();
  }

  /// Loads games from Hive database
  void _loadGames() {
    try {
      // Check if Hive is ready
      if (!GameService.isHiveReady()) {
        debugPrint('Hive is not ready, using fallback data');
        _setFallbackGames();
        return;
      }

      // Get games from Hive
      final gamesFromHive = GameService.getActiveGamesFromHive();
      print('Games from Hive: $gamesFromHive');
      setState(() {
        games = gamesFromHive.isNotEmpty ? gamesFromHive : _getFallbackGames();
        isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading games: $e');
      _setFallbackGames();
    }
  }

  /// Sets fallback games and updates state
  void _setFallbackGames() {
    setState(() {
      games = _getFallbackGames();
      isLoading = false;
    });
  }

  /// Returns fallback games when Hive data is not available
  List<Map<String, String>> _getFallbackGames() {
    return [
      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },      {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },     {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },     {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },     {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },     {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },     {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },     {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },     {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },     {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },     {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },     {
        'type': 'Texas Hold\'em',
        'name': 'Bluey World Series Poker Tournament',
        'blinds': '\$20/\$40',
        'buyIn': '\$2,500',
        'players': '8/9',
        'wait': '0:00',
        'hours': '4.3',
        'tableId': 'fallback_1',
      },
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 24, 24, 24),
      width: 1272,
      height: 700,
      decoration: BoxDecoration(
        color: Color(0xFF004447),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: Color(0xCC000000), width: 2.5),
      ),
      child: 
      Stack(
        clipBehavior: Clip.none,
        children: [
                Column(
        children: [
          HeaderComponent(),
          Expanded(
            child: isLoading
                ? Center(
                    child: CircularProgressIndicator(
                      color: Colors.white,
                    ),
                  )
                : GameListComponent(games: games),
          ),
          
        ],
      ),
               Positioned(
          left:-400,
          bottom:-330,
            child: Image.asset(
              'assets/images/leprachaun2.png',
              //width: 200,
              height: MediaQuery.of(context).size.height * 0.67,
              fit: BoxFit.contain,
              opacity: const AlwaysStoppedAnimation(1),
            ),
         ) ,

        Positioned(
          bottom: 0,
          child: FooterComponent(),
        )        ]

      ),

    );
  }
}

