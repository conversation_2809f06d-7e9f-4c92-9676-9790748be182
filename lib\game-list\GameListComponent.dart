import 'package:flutter/material.dart';

class GameListComponent extends StatefulWidget {
  final List<Map<String, String>> games;

  const GameListComponent({Key? key, required this.games}) : super(key: key);

  @override
  State<GameListComponent> createState() => _GameListComponentState();
}

class _GameListComponentState extends State<GameListComponent> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 24, 24, 24),
      margin: EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: const Color.fromARGB(255, 4, 36, 39),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: const Color(0xCC000000), width: 2.5),
      ),
      child: Column(
      
      children: [
        // Headers
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildHeader('Type', width: 95, left: true),
              _buildHeader('Name', width: 200),
              _buildHeader('Blinds', width: 95),
              _buildHeader('Buy-In', width: 95),
              _buildHeader('Players', width: 95),
              _buildHeader('WAIT', width: 95),
              _buildHeader('Hours', width: 95),
              _buildHeader('Action', width: 202),
            ],
          ),
        ),
        const SizedBox(height: 16),
        // Game List
        Expanded(
          child: RawScrollbar(
            controller: _scrollController,
            thumbVisibility: true,
            trackVisibility: true,
            thickness: 20,
            radius: const Radius.circular(10),
            thumbColor: Colors.transparent,
            trackColor: Colors.transparent,
            child: Stack(
              children: [
                ListView.builder(
                  controller: _scrollController,
                  itemCount: widget.games.length,
                  itemBuilder: (context, index) {
                    bool isAlternate = index % 2 == 0;
                    return _buildGameRow(widget.games[index], isAlternate);
                  },
                ),
                // Custom scrollbar track
                Positioned(
                  right: 0,
                  top: 0,
                  bottom: 0,
                  child: Container(
                    width: 20,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage('assets/images/scroll_pole.png'),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
                // Custom scrollbar thumb
                AnimatedBuilder(
                  animation: _scrollController,
                  builder: (context, child) {
                    if (!_scrollController.hasClients) return Container();

                    try {
                      final position = _scrollController.position;
                      final scrollExtent = position.maxScrollExtent;
                      final viewportHeight = position.viewportDimension;
                      final scrollOffset = _scrollController.offset;

                      // Add checks for valid values
                      if (scrollExtent <= 0 || viewportHeight <= 0) {
                        return Container();
                      }

                      final thumbHeight = (viewportHeight / (scrollExtent + viewportHeight)) * viewportHeight;
                      final thumbPosition = (scrollOffset / scrollExtent) * (viewportHeight - thumbHeight);

                      // Ensure calculations are valid numbers
                      if (thumbHeight.isNaN || thumbHeight.isInfinite ||
                          thumbPosition.isNaN || thumbPosition.isInfinite) {
                        return Container();
                      }

                      return Positioned(
                        right: 0,
                        top: thumbPosition.clamp(0, viewportHeight + 1250 ),
                        child: Container(
                          width: 20,
                          height: thumbHeight.clamp(50.0, 50),
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage('assets/images/scroll.png'),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      );
                    } catch (e) {
                      // If any error occurs during calculation, return empty container
                      return Container();
                    }
                  },
                ),
             
              ],
            ),
          ),
        ),
      ],
    )
 
    );
  }

  Widget _buildHeader(String text, {required double width, bool left = false}) {
    return Container(
      width: width,
      child: Text(
        text.toUpperCase(),
        style: const TextStyle(
          fontFamily: 'Inter',
          fontSize: 12,
          fontWeight: FontWeight.w800,
          color: Color.fromARGB(255, 153, 153, 153),
          letterSpacing: 0.5,
        ),
        textAlign: left ? TextAlign.left : TextAlign.center,
      ),
    );
  }

  Widget _buildGameRow(Map<String, String> game, bool isAlternate) {
    return Container(
      height: 80,
      margin: EdgeInsets.only(bottom: 10),
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        color: isAlternate ? Color.fromARGB(255, 6, 57, 61) : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        // border: Border.all(
        //   color: const Color(0x29FFFFFF),
        //   width: 2,
        // ),
        boxShadow: isAlternate
            ? [
                BoxShadow(
                  color: Colors.black.withOpacity(.5),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ]
            : null,
      ),
      child: Row(
      
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildGameText(game['type'] ?? '', width: 95, left: true),
          _buildGameText(game['name'] ?? '', width: 200),
          _buildGameText(game['blinds'] ?? '', width: 95),
          _buildGameText(game['buyIn'] ?? '', width: 95),
          _buildGameText(game['players'] ?? '', width: 95),
          _buildGameText(game['wait'] ?? '', width: 95),
          _buildGameText(game['hours'] ?? '', width: 95),
          Container(
            width: 202,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                _buildJoinButton(),
           
                _buildInfoButton(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameText(String text, {required double width, bool left = false}) {
    return                                                         Stack(
                                                            alignment: Alignment
                                                                .center,
                                                            children: [
                                                              Text(
                                                                 text,
                                                                style:
                                                                    TextStyle(
                                                                  fontSize: 14,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w900,
                                                                  foreground:
                                                                      Paint()
                                                                        ..style =
                                                                            PaintingStyle.stroke
                                                                        ..strokeWidth =
                                                                            4
                                                                        ..color =
                                                                            Colors.black,
                                                                ),
                                                                textAlign:
                                                                    TextAlign
                                                                        .center,
                                                              ),
                                                              Text(
                                                                text,
                                                                style:
                                                                    const TextStyle(
                                                                  fontSize: 14,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w900,
                                                                  color: Colors
                                                                      .white,
                                                                ),
                                                                textAlign:
                                                                    TextAlign
                                                                        .center,
                                                              )
                                                            ]);
  }

  Widget _buildJoinButton() {
    return Container(
      height: 48,
      child: MouseRegion(
      
        child: GestureDetector(
          onTap: () {},
          child: Image.asset('assets/images/ActionButton2.png'),
        ),
      ),
    );
  }

  Widget _buildInfoButton() {
        return Container(
      height: 48,
      child: MouseRegion(
      
        child: GestureDetector(
          onTap: () {},
          child: Image.asset('assets/images/ActionButton.png'),
        ),
      ),
    );
    
  }
}

